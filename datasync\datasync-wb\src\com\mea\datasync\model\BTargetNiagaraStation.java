// In: com.mea.datasync.model
package com.mea.datasync.model;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;

/**
 * BTargetNiagaraStation represents configuration for connecting to
 * target Niagara stations where synchronized components will be created.
 * This component handles only target station connection concerns.
 * 
 * Supports various connection methods:
 * - FoxS protocol connections
 * - SSL/TLS encrypted connections
 * - Authentication with username/password
 * - Custom port configurations
 */
@NiagaraType
@NiagaraProperty(
  name = "stationName",
  type = "baja:String",
  defaultValue = "BString.DEFAULT"
)
@NiagaraProperty(
  name = "hostAddress",
  type = "baja:String",
  defaultValue = "BString.DEFAULT"
)
@NiagaraProperty(
  name = "port",
  type = "baja:Integer",
  defaultValue = "BInteger.make(4911)"
)
@NiagaraProperty(
  name = "username",
  type = "baja:String",
  defaultValue = "BString.DEFAULT"
)
@NiagaraProperty(
  name = "basePath",
  type = "baja:String",
  defaultValue = "BString.make(\"station:|slot:/Drivers\")"
)
@NiagaraProperty(
  name = "useSSL",
  type = "baja:Boolean",
  defaultValue = "BBoolean.FALSE"
)
@NiagaraProperty(
  name = "connectionTimeout",
  type = "baja:Integer",
  defaultValue = "BInteger.make(30000)"
)
@NiagaraProperty(
  name = "lastConnectionTest",
  type = "baja:AbsTime",
  defaultValue = "BAbsTime.NULL"
)
@NiagaraProperty(
  name = "connectionStatus",
  type = "baja:String",
  defaultValue = "BString.make(\"Not Tested\")"
)
public class BTargetNiagaraStation extends BComponent {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.model.BTargetNiagaraStation(2047217648)1.0$ @*/
/* Generated Wed Jul 02 21:25:00 AEST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Property "stationName"

  /**
   * Slot for the {@code stationName} property.
   * @see #getStationName
   * @see #setStationName
   */
  public static final Property stationName = newProperty(0, BString.DEFAULT, null);

  /**
   * Get the {@code stationName} property.
   * @see #stationName
   */
  public String getStationName() { return getString(stationName); }

  /**
   * Set the {@code stationName} property.
   * @see #stationName
   */
  public void setStationName(String v) { 
    validateStationName(v);
    setString(stationName, v, null); 
  }

  //endregion Property "stationName"

  //region Property "hostAddress"

  /**
   * Slot for the {@code hostAddress} property.
   * @see #getHostAddress
   * @see #setHostAddress
   */
  public static final Property hostAddress = newProperty(0, BString.DEFAULT, null);

  /**
   * Get the {@code hostAddress} property.
   * @see #hostAddress
   */
  public String getHostAddress() { return getString(hostAddress); }

  /**
   * Set the {@code hostAddress} property.
   * @see #hostAddress
   */
  public void setHostAddress(String v) {
    validateHost(v);
    setString(hostAddress, v, null);
  }

  //endregion Property "hostAddress"

  //region Property "port"

  /**
   * Slot for the {@code port} property.
   * @see #getPort
   * @see #setPort
   */
  public static final Property port = newProperty(0, BInteger.make(4911).as(BInteger.class).getInt(), null);

  /**
   * Get the {@code port} property.
   * @see #port
   */
  public int getPort() { return getInt(port); }

  /**
   * Set the {@code port} property.
   * @see #port
   */
  public void setPort(int v) { 
    validatePort(v);
    setInt(port, v, null); 
  }

  //endregion Property "port"

  //region Property "username"

  /**
   * Slot for the {@code username} property.
   * @see #getUsername
   * @see #setUsername
   */
  public static final Property username = newProperty(0, BString.DEFAULT, null);

  /**
   * Get the {@code username} property.
   * @see #username
   */
  public String getUsername() { return getString(username); }

  /**
   * Set the {@code username} property.
   * @see #username
   */
  public void setUsername(String v) { 
    validateUsername(v);
    setString(username, v, null); 
  }

  //endregion Property "username"

  //region Property "basePath"

  /**
   * Slot for the {@code basePath} property.
   * @see #getBasePath
   * @see #setBasePath
   */
  public static final Property basePath = newProperty(0, BString.make("station:|slot:/Drivers"), null);

  /**
   * Get the {@code basePath} property.
   * @see #basePath
   */
  public String getBasePath() { return getString(basePath); }

  /**
   * Set the {@code basePath} property.
   * @see #basePath
   */
  public void setBasePath(String v) { 
    validateBasePath(v);
    setString(basePath, v, null); 
  }

  //endregion Property "basePath"

  //region Property "useSSL"

  /**
   * Slot for the {@code useSSL} property.
   * @see #getUseSSL
   * @see #setUseSSL
   */
  public static final Property useSSL = newProperty(0, BBoolean.FALSE, null);

  /**
   * Get the {@code useSSL} property.
   * @see #useSSL
   */
  public boolean getUseSSL() { return getBoolean(useSSL); }

  /**
   * Set the {@code useSSL} property.
   * @see #useSSL
   */
  public void setUseSSL(boolean v) { setBoolean(useSSL, v, null); }

  //endregion Property "useSSL"

  //region Property "connectionTimeout"

  /**
   * Slot for the {@code connectionTimeout} property.
   * @see #getConnectionTimeout
   * @see #setConnectionTimeout
   */
  public static final Property connectionTimeout = newProperty(0, BInteger.make(30000).as(BInteger.class).getInt(), null);

  /**
   * Get the {@code connectionTimeout} property.
   * @see #connectionTimeout
   */
  public int getConnectionTimeout() { return getInt(connectionTimeout); }

  /**
   * Set the {@code connectionTimeout} property.
   * @see #connectionTimeout
   */
  public void setConnectionTimeout(int v) { 
    validateConnectionTimeout(v);
    setInt(connectionTimeout, v, null); 
  }

  //endregion Property "connectionTimeout"

  //region Property "lastConnectionTest"

  /**
   * Slot for the {@code lastConnectionTest} property.
   * @see #getLastConnectionTest
   * @see #setLastConnectionTest
   */
  public static final Property lastConnectionTest = newProperty(0, BAbsTime.NULL, null);

  /**
   * Get the {@code lastConnectionTest} property.
   * @see #lastConnectionTest
   */
  public BAbsTime getLastConnectionTest() { return (BAbsTime)get(lastConnectionTest); }

  /**
   * Set the {@code lastConnectionTest} property.
   * @see #lastConnectionTest
   */
  public void setLastConnectionTest(BAbsTime v) { set(lastConnectionTest, v, null); }

  //endregion Property "lastConnectionTest"

  //region Property "connectionStatus"

  /**
   * Slot for the {@code connectionStatus} property.
   * @see #getConnectionStatus
   * @see #setConnectionStatus
   */
  public static final Property connectionStatus = newProperty(0, BString.make("Not Tested"), null);

  /**
   * Get the {@code connectionStatus} property.
   * @see #connectionStatus
   */
  public String getConnectionStatus() { return getString(connectionStatus); }

  /**
   * Set the {@code connectionStatus} property.
   * @see #connectionStatus
   */
  public void setConnectionStatus(String v) { setString(connectionStatus, v, null); }

  //endregion Property "connectionStatus"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTargetNiagaraStation.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Constants
////////////////////////////////////////////////////////////////

  public static final int DEFAULT_FOXS_PORT = 4911;
  public static final int DEFAULT_FOXS_SSL_PORT = 4912;
  public static final int MIN_PORT = 1;
  public static final int MAX_PORT = 65535;
  public static final int MIN_TIMEOUT = 1000;  // 1 second
  public static final int MAX_TIMEOUT = 300000; // 5 minutes
  
  public static final String CONNECTION_STATUS_NOT_TESTED = "Not Tested";
  public static final String CONNECTION_STATUS_SUCCESS = "Success";
  public static final String CONNECTION_STATUS_FAILED = "Failed";
  public static final String CONNECTION_STATUS_TIMEOUT = "Timeout";
  public static final String CONNECTION_STATUS_AUTH_FAILED = "Authentication Failed";

////////////////////////////////////////////////////////////////
// Validation Methods
////////////////////////////////////////////////////////////////

  /**
   * Validate the station name.
   * @param name the station name to validate
   * @throws IllegalArgumentException if the station name is invalid
   */
  private void validateStationName(String name) {
    if (name == null || name.trim().isEmpty()) {
      throw new IllegalArgumentException("Station name cannot be null or empty");
    }
    
    if (name.length() > 100) {
      throw new IllegalArgumentException("Station name cannot exceed 100 characters");
    }
    
    // Check for invalid characters
    if (!name.matches("^[a-zA-Z0-9\\s\\-_\\.]+$")) {
      throw new IllegalArgumentException("Station name contains invalid characters");
    }
  }

  /**
   * Validate the host address.
   * @param hostAddress the host to validate
   * @throws IllegalArgumentException if the host is invalid
   */
  private void validateHost(String hostAddress) {
    if (hostAddress == null || hostAddress.trim().isEmpty()) {
      throw new IllegalArgumentException("Host cannot be null or empty");
    }
    
    // Basic validation for hostname or IP address format
    if (!hostAddress.matches("^[a-zA-Z0-9.-]+$")) {
      throw new IllegalArgumentException("Host contains invalid characters");
    }
    
    if (hostAddress.length() > 255) {
      throw new IllegalArgumentException("Host cannot exceed 255 characters");
    }
  }

  /**
   * Validate the port number.
   * @param portNumber the port to validate
   * @throws IllegalArgumentException if the port is invalid
   */
  private void validatePort(int portNumber) {
    if (portNumber < MIN_PORT || portNumber > MAX_PORT) {
      throw new IllegalArgumentException("Port must be between " + MIN_PORT + " and " + MAX_PORT);
    }
  }

  /**
   * Validate the username.
   * @param user the username to validate
   * @throws IllegalArgumentException if the username is invalid
   */
  private void validateUsername(String user) {
    if (user == null || user.trim().isEmpty()) {
      throw new IllegalArgumentException("Username cannot be null or empty");
    }
    
    if (user.length() > 100) {
      throw new IllegalArgumentException("Username cannot exceed 100 characters");
    }
    
    // Check for invalid characters in username
    if (!user.matches("^[a-zA-Z0-9_.-]+$")) {
      throw new IllegalArgumentException("Username contains invalid characters");
    }
  }

  /**
   * Validate the base path.
   * @param path the base path to validate
   * @throws IllegalArgumentException if the base path is invalid
   */
  private void validateBasePath(String path) {
    if (path == null || path.trim().isEmpty()) {
      throw new IllegalArgumentException("Base path cannot be null or empty");
    }
    
    // Validate Niagara ORD format
    if (!path.startsWith("station:|slot:/")) {
      throw new IllegalArgumentException("Base path must start with 'station:|slot:/'");
    }
    
    if (path.length() > 500) {
      throw new IllegalArgumentException("Base path cannot exceed 500 characters");
    }
  }

  /**
   * Validate the connection timeout.
   * @param timeout the timeout to validate
   * @throws IllegalArgumentException if the timeout is invalid
   */
  private void validateConnectionTimeout(int timeout) {
    if (timeout < MIN_TIMEOUT || timeout > MAX_TIMEOUT) {
      throw new IllegalArgumentException("Connection timeout must be between " + 
                                       MIN_TIMEOUT + " and " + MAX_TIMEOUT + " milliseconds");
    }
  }

////////////////////////////////////////////////////////////////
// Connection Testing Methods
////////////////////////////////////////////////////////////////

  /**
   * Test the connection to the Niagara station.
   * @return true if connection is successful, false otherwise
   */
  public boolean testConnection() {
    try {
      // TODO: Issue #6 - Implement actual Niagara station connection testing
      // This is a placeholder implementation
      
      setLastConnectionTest(BAbsTime.now());
      
      // Simulate connection test based on configuration
      if (isValidConfiguration()) {
        setConnectionStatus(CONNECTION_STATUS_SUCCESS);
        return true;
      } else {
        setConnectionStatus(CONNECTION_STATUS_FAILED);
        return false;
      }
    } catch (Exception e) {
      setConnectionStatus(CONNECTION_STATUS_FAILED);
      setLastConnectionTest(BAbsTime.now());
      return false;
    }
  }

  /**
   * Check if the station configuration is valid.
   * @return true if all required fields are properly configured
   */
  public boolean isValidConfiguration() {
    try {
      return getHostAddress() != null && !getHostAddress().trim().isEmpty() &&
             getUsername() != null && !getUsername().trim().isEmpty() &&
             getBasePath() != null && !getBasePath().trim().isEmpty() &&
             getPort() >= MIN_PORT && getPort() <= MAX_PORT;
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * Get the connection URL for this station.
   * @return the FoxS connection URL
   */
  public String getConnectionUrl() {
    StringBuilder url = new StringBuilder();
    
    if (getUseSSL()) {
      url.append("foxs://");
    } else {
      url.append("fox://");
    }
    
    url.append(getHostAddress());
    
    // Only append port if it's not the default
    int currentPort = getPort();
    if ((getUseSSL() && currentPort != DEFAULT_FOXS_SSL_PORT) ||
        (!getUseSSL() && currentPort != DEFAULT_FOXS_PORT)) {
      url.append(":").append(currentPort);
    }
    
    return url.toString();
  }

  /**
   * Get a summary of the station configuration.
   * @return human-readable station summary
   */
  public String getStationSummary() {
    StringBuilder summary = new StringBuilder();
    
    if (getStationName() != null && !getStationName().trim().isEmpty()) {
      summary.append("Station: ").append(getStationName()).append(", ");
    }
    
    summary.append("Host: ").append(getHostAddress());
    summary.append(":").append(getPort());
    
    if (getUseSSL()) {
      summary.append(" (SSL)");
    }
    
    summary.append(", User: ").append(getUsername());
    summary.append(", Path: ").append(getBasePath());
    summary.append(", Status: ").append(getConnectionStatus());
    
    if (!getLastConnectionTest().isNull()) {
      summary.append(", Last Test: ").append(getLastConnectionTest());
    }
    
    return summary.toString();
  }

////////////////////////////////////////////////////////////////
// Security Methods
////////////////////////////////////////////////////////////////

  /**
   * TODO: Issue #1 - Implement secure password storage
   * This method will handle encrypted password storage and retrieval.
   * Currently passwords are not stored in this component for security.
   */
  public void setEncryptedPassword(String encryptedPassword) {
    // Future implementation for secure password storage
    throw new UnsupportedOperationException("Secure password storage not yet implemented");
  }

  /**
   * TODO: Issue #1 - Implement secure password retrieval
   * This method will handle decryption and retrieval of stored passwords.
   */
  public String getDecryptedPassword() {
    // Future implementation for secure password retrieval
    throw new UnsupportedOperationException("Secure password retrieval not yet implemented");
  }

////////////////////////////////////////////////////////////////
// Future Extension Points
////////////////////////////////////////////////////////////////

  /**
   * TODO: Issue #6 - Add certificate-based authentication support
   * This method will be implemented to support certificate-based
   * authentication for enhanced security.
   */
  public void setCertificateAuthentication(String certificatePath) {
    // Future implementation
    throw new UnsupportedOperationException("Certificate authentication not yet implemented");
  }

  /**
   * TODO: Issue #6 - Add connection pooling support
   * This method will be implemented to support connection pooling
   * for improved performance with multiple operations.
   */
  public void configureConnectionPool(int maxConnections, int idleTimeout) {
    // Future implementation
    throw new UnsupportedOperationException("Connection pooling not yet implemented");
  }
}
