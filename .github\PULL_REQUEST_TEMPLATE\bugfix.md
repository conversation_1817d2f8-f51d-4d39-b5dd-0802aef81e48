---
name: Bug Fix Pull Request
about: Pull request for bug fixes
---

## 🐛 Bug Fix Description

**Bug Summary:**
<!-- Brief description of the bug being fixed -->

**Related Issue(s):**
<!-- Link to GitHub issues: Fixes #123, Closes #456 -->

## 🔍 Root Cause Analysis

**What was the problem?**
<!-- Detailed explanation of what was causing the bug -->

**Why did it happen?**
<!-- Analysis of why the bug occurred -->

**How was it discovered?**
<!-- How the bug was found (user report, testing, etc.) -->

## 🛠️ Solution

**Changes Made:**
- [ ] List specific code changes
- [ ] List configuration changes
- [ ] List any workarounds removed

**Approach:**
<!-- Explain the approach taken to fix the bug -->

**Alternative Solutions Considered:**
<!-- Other approaches that were considered and why they were rejected -->

## 🧪 Testing

### Regression Testing
- [ ] Original bug scenario tested and fixed
- [ ] Related functionality tested
- [ ] Edge cases tested
- [ ] All existing tests pass

### Test Results
```
Paste test execution results here
```

### Manual Verification
- [ ] Bug reproduction steps no longer reproduce the issue
- [ ] Fix verified in Niagara Workbench
- [ ] No new issues introduced
- [ ] Performance impact assessed

## 📋 Verification Steps

**To verify the fix:**
1. Step 1: <!-- Describe verification steps -->
2. Step 2:
3. Step 3:

**Expected Result:**
<!-- What should happen after the fix -->

## 🔒 Security Impact

- [ ] No security implications
- [ ] Security review completed
- [ ] No new attack vectors introduced
- [ ] Input validation not affected

## 📚 Documentation

- [ ] Code comments updated if needed
- [ ] Troubleshooting guide updated
- [ ] Known issues documentation updated
- [ ] Release notes entry prepared

## 🎯 Quality Checklist

### Code Quality
- [ ] Minimal changes to fix the issue
- [ ] No unrelated changes included
- [ ] Error handling improved
- [ ] Code follows project standards

### Prevention Measures
- [ ] Tests added to prevent regression
- [ ] Code review process improvements identified
- [ ] Documentation gaps addressed
- [ ] Monitoring/alerting improvements considered

## 🚀 Deployment

**Urgency:**
- [ ] Critical (deploy immediately)
- [ ] High (deploy within 24 hours)
- [ ] Medium (deploy with next release)
- [ ] Low (deploy when convenient)

**Deployment Notes:**
<!-- Any special deployment considerations -->

**Rollback Plan:**
<!-- How to rollback if the fix causes issues -->

## 📊 Impact Assessment

**Users Affected:**
<!-- How many/which users were affected by this bug -->

**Business Impact:**
<!-- What was the business impact of this bug -->

**Risk Level:**
- [ ] Low risk fix
- [ ] Medium risk fix
- [ ] High risk fix (requires extra testing)

## 🔄 Follow-up Actions

- [ ] Monitor for related issues
- [ ] Update automated tests
- [ ] Review similar code for same issue
- [ ] Process improvements needed

**Additional Issues Created:**
<!-- Link to any follow-up issues created -->

## 👥 Reviewers

**Required Reviewers:**
- [ ] @username (code review)
- [ ] @username (QA review)

**Additional Context:**
<!-- Any additional information for reviewers -->
