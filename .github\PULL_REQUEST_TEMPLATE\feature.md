---
name: Feature Pull Request
about: Pull request for new features or enhancements
---

## 🚀 Feature Description

**Brief summary of the feature:**
<!-- Describe what this feature does -->

**Related Issue(s):**
<!-- Link to GitHub issues: Closes #123, Relates to #456 -->

## 📋 Changes Made

### New Components
- [ ] List new classes/components added
- [ ] List new interfaces created
- [ ] List new configuration files

### Modified Components
- [ ] List existing components modified
- [ ] List behavior changes
- [ ] List API changes

### Database/Schema Changes
- [ ] List any schema modifications
- [ ] List migration scripts needed
- [ ] List data transformation requirements

## 🧪 Testing

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

### Test Results
```
Paste test execution results here
```

### Manual Testing Checklist
- [ ] Feature works as expected in Niagara Workbench
- [ ] No regression in existing functionality
- [ ] Error handling works correctly
- [ ] Performance impact is acceptable

## 📚 Documentation

- [ ] Code comments added for complex logic
- [ ] Javadoc updated for public APIs
- [ ] README updated if needed
- [ ] Architecture documentation updated
- [ ] User documentation updated

## 🔒 Security Review

- [ ] No hardcoded credentials or sensitive data
- [ ] Input validation implemented
- [ ] Security implications considered
- [ ] Dependency security scan passed

## 🎯 Quality Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] No debug code or console.log statements
- [ ] Error handling is appropriate
- [ ] Performance considerations addressed

### Niagara Compliance
- [ ] Follows Niagara development patterns
- [ ] Proper @NiagaraType annotations used
- [ ] Component registration is correct
- [ ] Module dependencies properly declared

## 🚀 Deployment Notes

**Configuration Changes:**
<!-- List any configuration changes needed -->

**Migration Steps:**
<!-- List any manual steps needed for deployment -->

**Rollback Plan:**
<!-- Describe how to rollback if issues occur -->

## 📸 Screenshots/Demo

<!-- Add screenshots or GIFs showing the new feature -->

## 🔄 Breaking Changes

- [ ] This PR contains breaking changes
- [ ] Breaking changes are documented
- [ ] Migration guide provided

**Breaking Change Description:**
<!-- Describe any breaking changes and migration path -->

## 👥 Reviewers

**Required Reviewers:**
- [ ] @username (code review)
- [ ] @username (architecture review)
- [ ] @username (security review)

**Additional Context:**
<!-- Any additional information for reviewers -->
