// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;
import javax.baja.workbench.mgr.*;

import com.mea.datasync.model.BDataSourceConnection;
import com.mea.datasync.model.BDataSourceFolder;
import com.mea.datasync.model.BDataSourceConnectionsFolder;

/**
 * BDataSourceConnectionManager provides a standard Niagara manager view
 * for data source connections. This follows the same pattern as BDeviceManager,
 * BDriverManager, and BPointManager.
 *
 * Features automatically provided by BAbstractManager:
 * - Table view of all data source connections
 * - Add button for creating new connections (via getNewTypes())
 * - Context menu support with "New" option
 * - Delete, copy, paste operations
 * - Drag and drop support
 * - Export functionality
 */
@NiagaraType(
  agent = @AgentOn(
    types = { "datasync:DataSyncTool", "datasync:DataSourceFolder" }
  )
)
public class BDataSourceConnectionManager extends BAbstractManager {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSourceConnectionManager(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSourceConnectionManager.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// BAbstractManager Implementation
////////////////////////////////////////////////////////////////

  @Override
  protected MgrModel makeModel() {
    return new DataSourceConnectionModel(this);
  }

  @Override
  protected MgrController makeController() {
    return new MgrController(this);
  }

////////////////////////////////////////////////////////////////
// Manager Model - Defines table structure and data
////////////////////////////////////////////////////////////////

  /**
   * DataSourceConnectionModel manages the logical model of data source connections.
   * It defines the table columns, data display, and what types can be created.
   */
  class DataSourceConnectionModel extends MgrModel {

    DataSourceConnectionModel(BDataSourceConnectionManager manager) {
      super(manager);
    }

    @Override
    protected String makeTableTitle() {
      return "Data Source Connections";
    }

    @Override
    protected MgrColumn[] makeColumns() {
      return new MgrColumn[] {
        new MgrColumn.Name(),
        new MgrColumn.PropString("Type", "dataSourceTypeName", 0),
        new MgrColumn.PropString("Status", "connectionStatus", 0),
        new MgrColumn.PropString("Last Test", "lastConnectionTest", 0),
        new MgrColumn.PropString("Last Success", "lastSuccessfulConnection", 0),
        new MgrColumn.PropString("Failures", "consecutiveFailures", 0),
        new MgrColumn.PropString("Summary", "connectionSummary", 0)
      };
    }

    /**
     * Define what types can be created via "New" button and context menu.
     * This is the key method that enables automatic "New" functionality.
     * Using only BDataSourceConnection to avoid abstract class issues.
     */
    @Override
    public MgrTypeInfo[] getNewTypes() {
      return new MgrTypeInfo[] {
        MgrTypeInfo.make(BDataSourceConnection.TYPE),
        MgrTypeInfo.make(BDataSourceConnectionsFolder.TYPE)
      };
    }

    /**
     * Define what types are included in this manager view.
     * Using only BDataSourceConnection and folder types to avoid any
     * abstract class initialization issues.
     */
    @Override
    public Type[] getIncludeTypes() {
      return new Type[] {
        BDataSourceConnection.TYPE,
        BDataSourceConnectionsFolder.TYPE
      };
    }

    /**
     * Additional filtering for components displayed by this manager.
     * Accept only BDataSourceConnection and folders to avoid abstract class issues.
     */
    @Override
    public boolean accept(BComponent component) {
      return component instanceof BDataSourceConnection ||
             component instanceof BDataSourceConnectionsFolder;
    }

    /**
     * Set subscription depth to monitor child components.
     */
    @Override
    public int getSubscribeDepth() {
      return 2; // Monitor connections and their details
    }

    /**
     * Override load to handle both DataSyncTool and DataSourceConnections as target.
     */
    @Override
    public void load(BComponent target) {
      // If target is DataSyncTool, load its DataSourceConnections instead
      if (target instanceof com.mea.datasync.ui.BDataSyncTool) {
        com.mea.datasync.ui.BDataSyncTool tool = (com.mea.datasync.ui.BDataSyncTool) target;
        super.load(tool.getDataSourceConnections());
      } else {
        // If target is already DataSourceConnections, use it directly
        super.load(target);
      }
    }
  }
}
