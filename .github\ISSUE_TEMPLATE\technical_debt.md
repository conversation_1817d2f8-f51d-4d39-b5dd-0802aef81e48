---
name: Technical Debt
about: Track code quality and architecture improvements
title: '[DEBT] '
labels: tech-debt
assignees: ''
---

**Component**
Which part of the system needs improvement?

**Issue Description**
What's the problem with the current implementation?

**Impact**
- [ ] Security risk
- [ ] Maintainability issue
- [ ] Performance problem
- [ ] Code quality concern

**Proposed Solution**
How should this be fixed?

**Effort Estimate**
- [ ] Small (< 2 hours)
- [ ] Medium (2-8 hours)
- [ ] Large (> 8 hours)

**Priority**
- [ ] Must fix before MVP
- [ ] Should fix before production
- [ ] Nice to have improvement
