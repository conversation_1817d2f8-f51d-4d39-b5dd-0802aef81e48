/*
 * Copyright 2003, Tridium, Inc. All Rights Reserved.
 */

package javax.baja.collection;

import javax.baja.sys.BIObject;
import javax.baja.sys.IterableCursor;

/**
 * TableCursor is a cursor for iterating through the rows in a table.
 *
 * <AUTHOR> href="mailto:jsu<PERSON><EMAIL>"><PERSON></a>
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public interface TableCursor<T extends BIObject> extends IterableCursor<T>
{
  /**
   * Get the BITable backing this cursor (covariant return).
   */
  BITable<T> getTable();

  /**
   * Get the current table row (covariant return).
   */
  Row<T> row();

  /**
   * Get the cell value for the specified column at the current row.
   */
  default BIObject cell(Column column)
  {
    return row().cell(column);
  }
}
