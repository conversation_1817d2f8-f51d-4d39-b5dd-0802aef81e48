## Description

**Brief summary of changes:**
Describe what this PR does and why.

**Related Issues:**
Fixes #(issue number)
Relates to #(issue number)

## Type of Change

- [ ] 🐛 Bug fix (non-breaking change that fixes an issue)
- [ ] ✨ New feature (non-breaking change that adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🧪 Test improvements
- [ ] 🔧 Build/CI improvements
- [ ] 🎨 Code style/formatting changes
- [ ] ♻️ Refactoring (no functional changes)

## Changes Made

### Code Changes
- List specific code changes made
- Include new classes, methods, or significant modifications
- Note any API changes

### Configuration Changes
- Changes to build files, properties, or configuration
- New dependencies added or removed

### Documentation Changes
- Updates to README, guides, or inline documentation
- New documentation files created

## Testing

### Test Coverage
- [ ] Unit tests added/updated for new functionality
- [ ] Integration tests added/updated
- [ ] Manual testing completed in Niagara Workbench
- [ ] All existing tests pass

### Test Results
```
Paste test execution results here
```

### Manual Testing Steps
1. Step 1: Describe what you tested
2. Step 2: Expected vs actual results
3. Step 3: Any edge cases tested

## Quality Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Complex code has explanatory comments
- [ ] No debug code or console.log statements left
- [ ] Error handling is appropriate

### Niagara Compliance
- [ ] Follows Niagara development patterns
- [ ] Proper @NiagaraType annotations used
- [ ] Component registration is correct
- [ ] Module dependencies are properly declared

### Documentation
- [ ] Public APIs have Javadoc comments
- [ ] User-facing changes documented
- [ ] README updated if needed
- [ ] CHANGELOG.md updated

### Security
- [ ] No hardcoded credentials or sensitive data
- [ ] Input validation implemented where needed
- [ ] Security implications considered

## Deployment Notes

### Installation Impact
- [ ] No special installation steps required
- [ ] Module restart required
- [ ] Configuration migration needed
- [ ] Breaking changes require user action

### Rollback Plan
Describe how to rollback if issues are discovered after deployment.

## Screenshots (if applicable)

**Before:**
[Add screenshots of the current state]

**After:**
[Add screenshots of the changes]

## Additional Notes

**Performance Impact:**
Describe any performance implications.

**Future Considerations:**
Note any follow-up work or technical debt created.

**Review Focus Areas:**
Highlight specific areas where you'd like focused review attention.
