// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.agent.AgentList;
import javax.baja.nre.annotations.AgentOn;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.*;
import javax.baja.nav.BINavNode;
import javax.baja.workbench.tool.BWbNavNodeTool;
import com.mea.datasync.model.BDataSourceFolder;
import com.mea.datasync.model.BAbstractDataSourceConnection;
import java.util.List;
import java.util.ArrayList;

/**
 * BDataSyncTool serves as the main entry point for the N4-DataSync module within
 * Niagara Workbench. This tool automates the synchronization of data between external
 * data sources and Niagara station configurations, specifically for creating BACnet
 * Networks, Devices, Points, Extensions, etc.
 *
 * The tool extends BWbNavNodeTool to integrate with the Niagara Workbench environment,
 * appearing in the Tools menu and as a navigable node under the 'tool:' scheme.
 *
 * Supported Data Sources:
 * - Excel files (.xlsx, .xls)
 * - Google Sheets
 * - Grist databases
 * - Relational databases (PostgreSQL, MySQL, etc.)
 *
 * Key Features:
 * - Data source connection management
 * - Automated BACnet component creation
 * - Real-time synchronization capabilities
 * - Validation and error handling
 *
 * CRITICAL: This tool MUST be registered as an agent on "workbench:Workbench"
 * in module-include.xml to appear in the Tools menu. Views register as agents
 * on "datasync:DataSyncTool" to appear when the tool is opened.
 *
 * <AUTHOR> nguyen
 * @version 1.0.0
 * @since 1.0.0
 */
@NiagaraType
@AgentOn(types = "workbench:Workbench")
@NiagaraProperty(
  name = "dataSources",
  type = "datasync:DataSourceFolder",
  defaultValue = "new BDataSourceFolder()",
  flags = Flags.READONLY | Flags.SUMMARY
)
public class BDataSyncTool extends BWbNavNodeTool {

//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncTool(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Property "dataSources"

  /**
   * Slot for the {@code dataSources} property.
   * @see #getDataSources
   * @see #setDataSources
   */
  public static final Property dataSources = newProperty(0, new BDataSourceFolder(), null);

  /**
   * Get the {@code dataSources} property.
   * @see #dataSources
   */
  public BDataSourceFolder getDataSources() { return (BDataSourceFolder)get(dataSources); }

  /**
   * Set the {@code dataSources} property.
   * @see #dataSources
   */
  public void setDataSources(BDataSourceFolder v) { set(dataSources, v, null); }

  //endregion Property "dataSources"

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Subscriber for Enhanced Profile Changes
////////////////////////////////////////////////////////////////

  /**
   * Subscriber to listen for property changes on enhanced profile objects.
   * This ensures that when users modify profile properties, the changes are saved.
   */
  private final Subscriber profileChangeSubscriber = new Subscriber() {
    @Override
    public void event(BComponentEvent event) {
      if (event.getId() == BComponentEvent.PROPERTY_CHANGED) {
        BComponent source = event.getSourceComponent();

        // Note: Profile support removed - now only data source connections
      }
    }
  };

////////////////////////////////////////////////////////////////
// Lifecycle Methods
////////////////////////////////////////////////////////////////

  /**
   * Called when the tool is started.
   */
  @Override
  public void started() throws Exception {
    super.started();
    System.out.println("🚀 === BDataSyncTool: Tool started successfully! ===");
    System.out.println("🚀 Tool type: " + getType());
    System.out.println("🚀 Tool name: " + getName());
    System.out.println("🚀 Tool path: " + getSlotPath());
    System.out.println("🚀 === BDataSyncTool: Initialization complete ===");
  }

  /**
   * Called when a child component is parented to this tool.
   * Save new enhanced profiles.
   */
  @Override
  public void childParented(Property property, BValue newChild, Context context) {
    System.out.println("🔍 BDataSyncTool.childParented() called:");
    System.out.println("  Property: " + property.getName());
    System.out.println("  Child Type: " + newChild.getClass().getSimpleName());
    super.childParented(property, newChild, context);

    System.out.println("ℹ️ BDataSyncTool: Component added: " + newChild.getClass().getSimpleName());
  }

  /**
   * Called when a child component is unparented from this tool.
   * Unsubscribe from property changes.
   */
  @Override
  public void childUnparented(Property property, BValue oldChild, Context context) {
    System.out.println("🔍 BDataSyncTool.childUnparented() called:");
    System.out.println("  Property: " + property.getName());
    System.out.println("  Child Type: " + oldChild.getClass().getSimpleName());

    super.childUnparented(property, oldChild, context);

    System.out.println("ℹ️ BDataSyncTool: Component removed: " + oldChild.getClass().getSimpleName());
  }

  /**
   * Called when a property of this tool or its children changes.
   */
  @Override
  public void changed(Property property, Context context) {
    super.changed(property, context);

    // Enhanced profiles handle their own persistence through subscribers
    System.out.println("🔍 BDataSyncTool.changed() called for property: " + property.getName());
  }

  /**
   * Override getAgents to provide all default BComponent views plus our custom views.
   * This gives the DataSync Tool a rich set of views for managing data source connections.
   *
   * By default, BWbNavNodeTool hides all inherited agents to keep tools simple.
   * We override this to restore the full set of standard Niagara views.
   */
  @Override
  public AgentList getAgents(Context cx) {
    // Get the registry agents for BComponent to include all default views
    AgentList agents = Sys.getRegistry().getAgents(BComponent.TYPE.getTypeInfo());

    // Add our custom DataSync views
    agents.add("datasync:DataSyncProfileView");
    agents.add("datasync:DataSourceConnectionManager");

    // Set the view order - custom views first, then standard views
    agents.toTop("datasync:DataSyncProfileView");
    agents.toTop("datasync:DataSourceConnectionManager");

    System.out.println("🔍 BDataSyncTool.getAgents() returning " + agents.size() + " views:");
    for (int i = 0; i < agents.size(); i++) {
      System.out.println("  " + (i + 1) + ". " + agents.get(i).getAgentId());
    }

    return agents;
  }

////////////////////////////////////////////////////////////////
// Enhanced Profile Management
////////////////////////////////////////////////////////////////

  /**
   * Create a new Excel data source connection.
   * @param connectionName Unique name for the connection
   * @return true if successful
   */
  public boolean createExcelConnection(String connectionName) {
    try {
      System.out.println("🚀 Creating Excel connection: " + connectionName);

      // Create new Excel connection
      com.mea.datasync.model.BExcelDataSourceConnection connection =
        new com.mea.datasync.model.BExcelDataSourceConnection();

      // Configure connection details
      com.mea.datasync.model.BExcelConnectionDetails details = connection.getConnectionDetails();
      details.setConnectionName(connectionName);
      details.setDescription("Excel data source connection");
      details.setFilePath("C:\\DataSync\\" + connectionName.toLowerCase() + ".xlsx");
      details.setDefaultWorksheet("Sheet1");

      // Add to data sources container
      BDataSourceFolder connections = getDataSources();
      connections.add(connectionName, connection);

      System.out.println("✅ Excel connection created successfully: " + connectionName);
      return true;

    } catch (Exception e) {
      System.err.println("❌ Error creating Excel connection: " + e.getMessage());
      e.printStackTrace();
      return false;
    }
  }



////////////////////////////////////////////////////////////////
// Actions for UI Integration
////////////////////////////////////////////////////////////////

  /**
   * Action to create a new Excel connection from the UI.
   */
  public void doCreateExcelConnection() {
    try {
      String connectionName = "ExcelConnection_" + System.currentTimeMillis();
      boolean success = createExcelConnection(connectionName);

      if (success) {
        System.out.println("🎉 Excel connection created from UI: " + connectionName);
        System.out.println("📍 Check the nav tree under DataSync Tool for the new connection");
      } else {
        System.err.println("❌ Failed to create Excel connection from UI");
      }
    } catch (Exception e) {
      System.err.println("❌ Error in doCreateExcelConnection: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Get the number of data source connections.
   * @return Connection count
   */
  public int getConnectionCount() {
    BDataSourceConnections connections = getDataSources();
    return connections != null ? connections.getAllDataSourceConnections().length : 0;
  }

////////////////////////////////////////////////////////////////
// Navigation Tree Integration
////////////////////////////////////////////////////////////////

  /**
   * Enhanced navigation children to include data sources.
   */
  @Override
  public BINavNode[] getNavChildren() {
    java.util.List<BINavNode> children = new java.util.ArrayList<>();

    // Add data sources container
    BDataSourceFolder connections = getDataSources();
    if (connections != null) {
      children.add(connections);
    }

    // Note: Removed profile support - now only data sources

    return children.toArray(new BINavNode[0]);
  }

  /**
   * Enhanced tool description including data source connection status.
   */
  @Override
  public String getNavDescription(Context cx) {
    StringBuilder desc = new StringBuilder();
    desc.append("N4-DataSync Tool");

    // Add data source summary
    BDataSourceConnections connections = getDataSources();
    if (connections != null) {
      int connectionCount = connections.getDataSourceConnectionCount();
      int healthyCount = 0;

      // Count healthy connections
      BAbstractDataSourceConnection[] allConnections = connections.getAllDataSourceConnections();
      for (BAbstractDataSourceConnection conn : allConnections) {
        if (conn.isConnectionHealthy()) {
          healthyCount++;
        }
      }

      if (connectionCount > 0) {
        desc.append(" - ").append(connectionCount).append(" data source");
        if (connectionCount != 1) desc.append("s");
        desc.append(" (").append(healthyCount).append(" healthy)");
      }
    }

    return desc.toString();
  }

  @Override
  public BIcon getNavIcon() {
    return BIcon.std("tool.png");
  }
}
