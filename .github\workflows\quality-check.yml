name: Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false

jobs:
  quality-check:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for better analysis

    - name: Set up JDK 8
      uses: actions/setup-java@v4
      with:
        java-version: '8'
        distribution: 'zulu'  # Match development environment

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Validate Gradle wrapper
      uses: gradle/wrapper-validation-action@v1

    - name: Check for unreferenced TODOs
      run: ./gradlew checkTodos

    - name: Generate tech debt report
      run: ./gradlew techDebtReport

    - name: Run tests with coverage
      run: ./gradlew test jacocoTestReport
      continue-on-error: true  # Don't fail build on test failures initially

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          **/build/test-results/
          **/build/reports/

    - name: Build project
      run: ./gradlew build -x test  # Skip tests since we ran them separately

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          **/build/libs/
          **/build/distributions/
