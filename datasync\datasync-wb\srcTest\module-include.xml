<!-- Test Module Include File for N4-DataSync -->
<!-- This file registers all test classes for the Niagara test runner -->

<types>
  <!-- Test Classes -->
  <type class="com.mea.datasync.test.BDataSourceConnectionTest" name="DataSourceConnectionTest"/>
  <type class="com.mea.datasync.test.BAbstractDataSourceConnectionTest" name="AbstractDataSourceConnectionTest"/>
  <type class="com.mea.datasync.test.BExcelDataSourceConnectionTest" name="ExcelDataSourceConnectionTest"/>
  <type class="com.mea.datasync.test.BAutoCheckConfigTest" name="AutoCheckConfigTest"/>
  <type class="com.mea.datasync.test.BDataSourceConnectionsTest" name="DataSourceConnectionsTest"/>
  <type class="com.mea.datasync.test.BDataSyncToolIntegrationTest" name="DataSyncToolIntegrationTest"/>
  <type class="com.mea.datasync.test.BDataSourceConnectionManagerViewTest" name="DataSourceConnectionManagerViewTest"/>
  <type class="com.mea.datasync.test.BDataSyncRuntimeIntegrationTest" name="DataSyncRuntimeIntegrationTest"/>
  
  <!-- Test Utility Classes -->
  <type class="com.mea.datasync.test.utils.BaseTestClass" name="BaseTestClass"/>
  <type class="com.mea.datasync.test.utils.BIntegrationTestBase" name="IntegrationTestBase"/>
  <type class="com.mea.datasync.test.utils.BPerformanceTestBase" name="PerformanceTestBase"/>
  <type class="com.mea.datasync.test.utils.BUITestBase" name="UITestBase"/>
  
  <!-- Test Data Factory Classes -->
  <type class="com.mea.datasync.test.data.ExcelTestDataFactory" name="ExcelTestDataFactory"/>
</types>
