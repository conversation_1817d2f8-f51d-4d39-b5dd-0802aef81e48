============================================================
PAGE 57
============================================================

# Spy
## Overview
 The Niagara Framework is built upon a principle of high visibility. By modeling everything as a BObjects most data and
 functionality is automatically made visible using the tools built into the workbench. However it is infeasible to model all
 data using the component model. The spy framework provides a diagnostics window into the system internals for
 debugging which goes beyond the component model.
 Spy pages are accessed via the spy:/ ord.
 See javax.baja.spy package for more details.
