name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1'  # Weekly on Monday at 2 AM

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up JDK 8
      uses: actions/setup-java@v4
      with:
        java-version: '8'
        distribution: 'zulu'
        
    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Run dependency vulnerability scan
      run: ./gradlew dependencyCheckAnalyze
      continue-on-error: true
      
    - name: Upload dependency check results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: dependency-check-report
        path: build/reports/dependency-check-report.html
        
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: java
        
    - name: Build for CodeQL
      run: ./gradlew build -x test
      
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      
    - name: Check for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
